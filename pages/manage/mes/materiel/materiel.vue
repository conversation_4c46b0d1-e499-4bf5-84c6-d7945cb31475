<template>
  <div class="materiel-container">
    <a-card title="物料管理">
      <template #extra>
        <a-button type="primary" @click="handleAddMateriel">
          新增物料
        </a-button>
      </template>

      <!-- 表格区域 -->
      <manage-base-table
        :columns="columns"
        :query="fetchMaterielList"
        :model="searchParams"
        ref="tableRef"
      >
        <template #searchBox>
          <a-form-item name="code" label="编码">
            <manage-base-search-input
              v-model:value="searchParams.code"
              placeholder="请输入编码"
            />
          </a-form-item>
          <a-form-item name="name" label="名称">
            <manage-base-search-input
              v-model:value="searchParams.name"
              placeholder="请输入名称"
            />
          </a-form-item>
          <a-form-item name="model" label="型号">
            <manage-base-search-input
              v-model:value="searchParams.model"
              placeholder="请输入型号"
            />
          </a-form-item>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name'">
            <a-tooltip :title="record.description" placement="right">
              <a>{{ record.name }}</a>
            </a-tooltip>
          </template>

          <template v-if="column.key === 'useable'">
            <a-tag :color="record.useable ? 'green' : 'red'">
              {{ record.useable ? "启用" : "停用" }}
            </a-tag>
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a @click="handleEditMateriel(record)">编辑</a>
              <a-divider type="vertical" />
              <a @click="bomModal.open(record)">查看BOM</a>
            </a-space>
          </template>
        </template>
      </manage-base-table>
    </a-card>

    <!-- 物料编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="800px"
      :confirmLoading="confirmLoading"
    >
      <a-form
        :model="materielForm"
        :rules="createMaterielRules"
        ref="formRef"
        layout="vertical"
        @submit.prevent="handleModalOk"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="code" label="物料编码">
              <a-input
                v-model:value="materielForm.code"
                placeholder="物料编码将根据物料分类自动生成"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="name" label="物料名称">
              <a-input
                v-model:value="materielForm.name"
                placeholder="请输入物料名称"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="model" label="物料型号">
              <a-input
                v-model:value="materielForm.model"
                placeholder="请输入物料型号"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="unit" label="计量单位">
              <a-input
                v-model:value="materielForm.unit"
                placeholder="请输入计量单位"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item name="specification" label="物料规格">
              <a-input
                v-model:value="materielForm.specification"
                placeholder="请输入物料规格"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="typeArray" label="物料分类">
              <manage-materiel-category-selector
                v-model:value="materielForm.typeArray"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item name="description" label="物料描述">
          <a-textarea
            v-model:value="materielForm.description"
            placeholder="请输入物料描述"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- BOM查看弹窗 -->
    <a-modal v-model:open="bomOpen" title="物料BOM" width="80%">
      <!-- 物料基本信息 -->
      <a-row :gutter="16">
        <a-col :span="24">
          <a-descriptions :column="3" title="物料信息">
            <a-descriptions-item label="物料编码">{{
              bomModal.data.code
            }}</a-descriptions-item>
            <a-descriptions-item label="物料名称">{{
              bomModal.data.name
            }}</a-descriptions-item>
            <a-descriptions-item label="物料型号">{{
              bomModal.data.model
            }}</a-descriptions-item>
            <a-descriptions-item label="规格">{{
              bomModal.data.specification
            }}</a-descriptions-item>
            <a-descriptions-item label="单位">{{
              bomModal.data.unit
            }}</a-descriptions-item>
          </a-descriptions>
        </a-col>
      </a-row>

      <!-- BOM结构列表 -->
      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="24">
          <a-tabs v-model:activeKey="activeKey">
            <a-tab-pane key="1">
              <template #tab> 物料清单 ({{ bom.length }}) </template>
              <manage-materiel-form-table
                v-model:source="bom"
                :has-note="true"
                :has-price="false"
                :has-delete="true"
                :is-view="false"
              />
            </a-tab-pane>
            <a-tab-pane key="2">
              <template #tab> 副产物列表 ({{ subProduct.length }}) </template>
              <manage-materiel-form-table
                v-model:source="subProduct"
                :has-delete="true"
              />
            </a-tab-pane>
          </a-tabs>
        </a-col>
      </a-row>

      <template #footer>
        <a-button @click="bomOpen = false">取消</a-button>
        <a-button type="primary" @click="bomModal.save()">保存</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed, h } from "vue";
  import type { TablePaginationConfig } from "ant-design-vue";
  import type { TableColumnsType, TableColumnType } from "ant-design-vue";
  import type { FormInstance } from "ant-design-vue";
  import { message } from "ant-design-vue";
  import type { RuleObject } from "ant-design-vue/es/form/interface";
  import { useForm } from "ant-design-vue/es/form";
  import { z } from "zod";
  import type { BomCreateInput } from "@/schemas/bom";
  import Decimal from "decimal.js";
  // API客户端
  // const client = useNuxtApp().$client;
  const client = useApiTrpc();
  const tableRef = ref();

  const activeKey = ref("1");

  // 搜索参数
  const searchParams = reactive({
    code: undefined,
    name: undefined,
    model: undefined,
  });

  // 表格列定义
  const columns: ExtendedTableColumnProps<Materiel>[] = [
    { title: "编码", dataIndex: "code", key: "code" },
    { title: "名称", dataIndex: "name", key: "name" },
    { title: "型号", dataIndex: "model", key: "model" },
    { title: "单位", dataIndex: "unit", key: "unit" },
    { title: "规格", dataIndex: "specification", key: "specification" },
    { title: "状态", dataIndex: "useable", key: "useable" },
    {
      title: "创建时间",
      dataIndex: "createAt",
      key: "createAt",
      defaultVisible: false,
      customRender: ({ text }) => {
        return formatDateTime(text);
      },
    },
    {
      title: "更新时间",
      dataIndex: "updateAt",
      key: "updateAt",
      defaultVisible: false,
      customRender: ({ text }: { text: string }) => {
        return formatDateTime(text);
      },
    },
    { title: "操作", key: "action" },
  ];

  // 格式化日期时间
  const formatDateTime = (time: string | Date) => {
    return new Date(time).toLocaleString("zh-CN", {
      timeZone: "Asia/Shanghai",
    });
  };

  const modalVisible = ref(false);
  const confirmLoading = ref(false);
  const modalTitle = ref("新增物料");
  const currentMaterielId = ref<number | null>(null);

  // 物料表单数据
  const materielForm = reactive({
    id: undefined as number | undefined,
    code: "",
    name: "",
    model: "",
    unit: "",
    specification: "",
    typeArray: ["01", "01", "00"] as [string, string, string],
    category: "",
    attribute: "",
    type: "",
    description: "",
    useable: true,
  });
  // 物料表单相关
  const formRef = ref<FormInstance>();

  const createMaterielRules = zodObjectToAntRules(materielCreateSchema);

  type Bom = MaterielListItem[];
  type SubProduct = MaterielListItem[];
  const bom = ref<Bom>([] as Bom);
  const subProduct = ref<SubProduct>([] as SubProduct);

  const bomOpen = ref(false);

  const bomModal = reactive({
    visible: false,
    data: {} as MaterielListItem,
    isEdit: false,
    open: async (record: MaterielListItem) => {
      // bomModal.visible = true;
      bomOpen.value = true;
      bomModal.data = record;
      const bomResponse = await client.admin.bom.queryBom.query({
        materielId: bomModal.data.id,
      });

      if (bomResponse.code === 1) {
        if (bomResponse.data.result.length > 0) {
          bom.value = bomResponse.data.result.map((item) => ({
            id: item.child.id,
            code: item.child.code,
            name: item.child.name,
            model: item.child.model,
            unit: item.child.unit,
            specification: item.child.specification,
            quantity: item.quantity || 0,
            note: item.remark,
          })) as Bom;
          bomModal.isEdit = true;
        } else {
          bom.value = [];
          bomModal.isEdit = false;
          message.warning("物料BOM为空，请添加BOM");
        }
      } else {
        message.error(bomResponse.message || "BOM查询失败");
      }
      const subProductResponse = await client.admin.bom.querySubBom.query({
        materielId: bomModal.data.id,
      });

      if (subProductResponse.code === 1) {
        subProduct.value = subProductResponse.data.result.map((item) => ({
          id: item.child.id,
          code: item.child.code,
          name: item.child.name,
          model: item.child.model,
          unit: item.child.unit,
          specification: item.child.specification,
          quantity: item.quantity || 0,
          note: item.remark,
        })) as SubProduct;
      } else {
        message.error(subProductResponse.message || "副产物BOM查询失败");
      }
    },
    close: () => {
      bomOpen.value = false;
    },
    save: async () => {
      console.log("materiel:", bomModal.data);
      console.log("bom:", bom.value);
      const input: BomCreateInput = {
        materielId: bomModal.data.id,
        bom: bom.value.map((item) => ({
          materielId: item.id,
          quantity: new Decimal(item.quantity || 0),
          note: item.note || "",
        })),
        subProduct: subProduct.value.map((item) => ({
          materielId: item.id,
          quantity: new Decimal(item.quantity || 0),
          note: item.note || "",
        })),
      };
      if (bomModal.isEdit) {
        const response = await client.admin.bom.updateBom.mutate(input);
        if (response.code === 1) {
          message.success("BOM更新成功");
          bomOpen.value = false;
        } else {
          message.error(response.message || "BOM更新失败");
        }
      } else {
        const response = await client.admin.bom.createBom.mutate(input);
        if (response.code === 1) {
          message.success("BOM创建成功");
          bomOpen.value = false;
        } else {
          message.error(response.message || "BOM创建失败");
        }
      }
    },
  });

  // 获取物料列表
  const fetchMaterielList = async (params: any) => {
    try {
      const response = await client.admin.materiel.queryMateriel.query({
        ...searchParams,
        ...params,
      });

      if (response.code === 1) {
        return {
          data: {
            result: response.data.result || [],
            total: response.data.total || 0,
          },
        };
      } else {
        message.error(response.message || "获取物料列表失败");
        return {
          data: {
            result: [],
            total: 0,
          },
        };
      }
    } catch (error: any) {
      message.error(error.message || "获取物料列表失败");
      return {
        data: {
          result: [],
          total: 0,
        },
      };
    }
  };

  // 新增物料
  const handleAddMateriel = () => {
    resetForm();
    modalTitle.value = "新增物料";
    modalVisible.value = true;
  };

  // 编辑物料
  const handleEditMateriel = (record: any) => {
    materielForm.id = record.id;
    materielForm.code = record.code;
    materielForm.name = record.name;
    materielForm.model = record.model;
    materielForm.unit = record.unit;
    materielForm.specification = record.specification;
    materielForm.typeArray = [record.category, record.attribute, record.type];
    materielForm.description = record.description || "";
    materielForm.useable = record.useable;

    modalTitle.value = "编辑物料";
    modalVisible.value = true;
  };

  // 弹窗确认处理
  const handleModalOk = () => {
    formRef.value
      ?.validate()
      .then(async () => {
        confirmLoading.value = true;

        // 从级联选择器中提取分类信息
        const [category, attribute, type] = materielForm.typeArray;
        try {
          if (materielForm.id) {
            const input: MaterielUpdateInput = {
              id: materielForm.id,
              name: materielForm.name,
              model: materielForm.model,
              category,
              attribute,
              type,
              code: materielForm.code,
              unit: materielForm.unit,
              specification: materielForm.specification,
              useable: materielForm.useable,
              description: materielForm.description,
            };
            // 编辑物料
            const response = await client.admin.materiel.updateMateriel.mutate(
              input
            );

            if (response.code === 1) {
              message.success("物料更新成功");
              modalVisible.value = false;
              tableRef.value?.query();
            } else {
              message.error(response.message || "物料更新失败");
            }
          } else {
            const input: MaterielCreateInput = {
              name: materielForm.name,
              model: materielForm.model,
              type: materielForm.typeArray,
              code: materielForm.code,
              unit: materielForm.unit,
              specification: materielForm.specification,
              description: materielForm.description,
            };
            // 新增物料
            const response = await client.admin.materiel.addMateriel.mutate(
              input
            );

            if (response.code === 1) {
              message.success("物料添加成功");
              modalVisible.value = false;
              tableRef.value?.query();
            } else {
              message.error(response.message || "物料添加失败");
            }
          }
        } catch (error: any) {
          message.error(error.message || "操作失败");
        } finally {
          confirmLoading.value = false;
        }
      })
      .catch((error) => {
        console.log("验证失败:", error);
      });
  };

  // 弹窗取消处理
  const handleModalCancel = () => {
    modalVisible.value = false;
    resetForm();
  };

  // 重置表单
  const resetForm = () => {
    materielForm.id = undefined;
    materielForm.code = "";
    materielForm.name = "";
    materielForm.model = "";
    materielForm.unit = "";
    materielForm.specification = "";
    materielForm.typeArray = ["01", "01", "00"];
    materielForm.description = "";
    materielForm.useable = true;

    formRef.value?.resetFields();
  };
</script>

<style scoped>
  /* .materiel-container {
    padding: 0 10px;
  } */
</style>
