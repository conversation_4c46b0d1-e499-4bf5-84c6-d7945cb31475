<template>
  <a-card title="库存查询">
    <!-- 数据表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      :expandable="expandableConfig"
      row-key="id"
      bordered
      size="small"
      @change="handleTableChange"
    >
      <template #expandedRowRender="{ record }">
        <a-table
          :columns="batchColumns"
          :data-source="record.batches"
          :pagination="false"
          size="small"
          bordered
          row-key="id"
        />
      </template>
    </a-table>

    <!-- 搜索条件 -->
    <a-drawer
      v-model:open="searchDrawerVisible"
      title="搜索条件"
      placement="right"
      width="400"
    >
      <a-form :model="searchForm" layout="vertical" @finish="handleSearch">
        <a-form-item name="materialCode" label="物料编码">
          <a-input
            v-model:value="searchForm.materialCode"
            placeholder="请输入物料编码"
            allowClear
          />
        </a-form-item>
        <a-form-item name="materialName" label="物料名称">
          <a-input
            v-model:value="searchForm.materialName"
            placeholder="请输入物料名称"
            allowClear
          />
        </a-form-item>
        <a-form-item name="warehouseId" label="仓库">
          <a-select
            v-model:value="searchForm.warehouseId"
            placeholder="请选择仓库"
            allowClear
          >
            <a-select-option
              v-for="item in warehouseOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="batchNo" label="批号">
          <a-input
            v-model:value="searchForm.batchNo"
            placeholder="请输入批号"
            allowClear
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="loading">
              查询
            </a-button>
            <a-button @click="handleReset"> 重置 </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-drawer>

    <!-- 操作按钮 -->
    <a-affix :offset-bottom="20">
      <a-float-button-group shape="circle" style="right: 24px">
        <a-float-button tooltip="搜索" @click="searchDrawerVisible = true">
          <template #icon>
            <SearchOutlined />
          </template>
        </a-float-button>
        <a-float-button tooltip="刷新" @click="fetchData" :loading="loading">
          <template #icon>
            <ReloadOutlined />
          </template>
        </a-float-button>
      </a-float-button-group>
    </a-affix>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from "vue";
  import { SearchOutlined, ReloadOutlined } from "@ant-design/icons-vue";
  import type { PaginationProps, TableProps } from "ant-design-vue";
  import { z } from "zod";

  // 验证schema
  const searchSchema = z.object({
    materialCode: z.string().optional(),
    materialName: z.string().optional(),
    warehouseId: z.number().optional(),
    batchNo: z.string().optional(),
  });

  // 搜索表单
  const searchForm = reactive({
    materialCode: undefined, // 物料编码
    materialName: undefined, // 物料名称
    warehouseId: undefined, // 仓库ID
    batchNo: undefined, // 批号
  });

  // 页面状态
  const loading = ref(false);
  const dataSource = ref<any[]>([]);
  const searchDrawerVisible = ref(false);
  const current = ref(1);
  const total = ref(0);
  const pageSize = 10;

  // 仓库选项
  const warehouseOptions = ref<{ label: string; value: number }[]>([]);

  // 分页配置
  const pagination = computed<PaginationProps>(() => ({
    current: current.value,
    total: total.value,
    pageSize: pageSize,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    showSizeChanger: false,
  }));

  // 展开配置
  const expandableConfig = {
    expandRowByClick: true,
    rowExpandable: (record: any) => record.batches && record.batches.length > 0,
  };

  // 获取仓库列表
  const fetchWarehouseList = async () => {
    try {
      const queryWarehouseList =
        useApiTrpc().admin.warehouse.queryWarehouseList.query;
      const response = await queryWarehouseList({
        take: 100, // 获取最多100个仓库
        skip: 0,
      });

      if (response.data && response.data.result) {
        warehouseOptions.value = response.data.result.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
            disabled: item.lock, // 如果仓库被锁定，则禁用选项
          };
        });
      }
    } catch (error) {
      console.error("获取仓库列表失败", error);
    }
  };

  // 页面加载时获取仓库列表
  onMounted(async () => {
    await fetchWarehouseList();
    await fetchData();
  });

  // 主表格列定义
  const columns = [
    {
      title: "物料编码",
      dataIndex: ["materiel", "code"],
      width: 120,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 150,
    },
    {
      title: "规格型号",
      dataIndex: ["materiel", "specification"],
      width: 120,
    },
    {
      title: "总库存数量",
      dataIndex: "totalQuantity",
      width: 120,
      customRender: ({ text }: any) => {
        return Number(text).toFixed(4);
      },
    },
    {
      title: "单位",
      dataIndex: ["materiel", "unit"],
      width: 80,
    },
    {
      title: "批次数量",
      dataIndex: "batches",
      width: 100,
      customRender: ({ text }: any) => {
        return `${text?.length || 0} 个批次`;
      },
    },
  ];

  // 批次详情表格列定义
  const batchColumns = [
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 120,
    },
    {
      title: "数量",
      dataIndex: "quantity",
      width: 100,
      customRender: ({ text }: any) => {
        return Number(text).toFixed(4);
      },
    },
    {
      title: "仓库",
      dataIndex: ["warehouse", "name"],
      width: 120,
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 80,
      customRender: ({ text }: any) => {
        const statusMap: Record<string, string> = {
          normal: "正常",
          locked: "锁定",
          expired: "过期",
        };
        return statusMap[text] || text;
      },
    },
  ];

  // API接口
  const queryStockGrouped = useApiTrpc().admin.stock.queryStockGrouped.query;

  // 获取数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const params = {
        ...searchForm,
        take: pageSize,
        skip: (current.value - 1) * pageSize,
      };

      const response = await queryStockGrouped(params);

      if (response.code === 1 && response.data) {
        dataSource.value = response.data.result || [];
        total.value = response.data.total || 0;
      }
    } catch (error) {
      console.error("获取库存数据失败", error);
    } finally {
      loading.value = false;
    }
  };

  // 表格变化处理
  const handleTableChange: TableProps["onChange"] = (pag: PaginationProps) => {
    current.value = pag.current || 1;
    fetchData();
  };

  // 搜索处理
  const handleSearch = () => {
    current.value = 1;
    fetchData();
    searchDrawerVisible.value = false;
  };

  // 重置处理
  const handleReset = () => {
    searchForm.materialCode = undefined;
    searchForm.materialName = undefined;
    searchForm.warehouseId = undefined;
    searchForm.batchNo = undefined;
    handleSearch();
  };
</script>

<style scoped>
  .stock-query {
    padding: 24px;
  }
</style>
