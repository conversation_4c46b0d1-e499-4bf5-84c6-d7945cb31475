<template>
  <a-card title="库存查询">
    <!-- 数据表格 -->
    <manage-base-table
      ref="tableRef"
      :columns="columns"
      :query="query"
      :model="searchForm"
    >
      <template #searchBox>
        <a-form-item name="materialCode" label="物料编码">
          <a-input
            v-model:value="searchForm.materialCode"
            placeholder="请输入物料编码"
            allowClear
          />
        </a-form-item>
        <a-form-item name="materialName" label="物料名称">
          <a-input
            v-model:value="searchForm.materialName"
            placeholder="请输入物料名称"
            allowClear
          />
        </a-form-item>
        <a-form-item name="warehouseId" label="仓库">
          <a-select
            v-model:value="searchForm.warehouseId"
            placeholder="请选择仓库"
            allowClear
          >
            <a-select-option
              v-for="item in warehouseOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="batchNo" label="批号">
          <a-input
            v-model:value="searchForm.batchNo"
            placeholder="请输入批号"
            allowClear
          />
        </a-form-item>
      </template>
    </manage-base-table>
  </a-card>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from "vue";
  import BaseTable from "~/components/manage/base/table.client.vue";
  import { z } from "zod";

  // 验证schema
  const searchSchema = z.object({
    materialCode: z.string().optional(),
    materialName: z.string().optional(),
    warehouseId: z.number().optional(),
    batchNo: z.string().optional(),
  });

  // 搜索表单
  const searchForm = reactive({
    materialCode: undefined, // 物料编码
    materialName: undefined, // 物料名称
    warehouseId: undefined, // 仓库ID
    batchNo: undefined, // 批号
  });

  // 仓库选项
  const warehouseOptions = ref<{ label: string; value: number }[]>([]);

  // 获取仓库列表
  const fetchWarehouseList = async () => {
    try {
      const queryWarehouseList =
        useApiTrpc().admin.warehouse.queryWarehouseList.query;
      const response = await queryWarehouseList({
        take: 100, // 获取最多100个仓库
        skip: 0,
      });

      if (response.data && response.data.result) {
        warehouseOptions.value = response.data.result.map((item: any) => {
          return {
            value: item.id,
            label: item.name,
            disabled: item.lock, // 如果仓库被锁定，则禁用选项
          };
        });
      }
    } catch (error) {
      console.error("获取仓库列表失败", error);
    }
  };

  // 页面加载时获取仓库列表
  onMounted(async () => {
    await fetchWarehouseList();
  });

  // 表格列定义
  const columns = [
    {
      title: "物料编码",
      dataIndex: ["materiel", "code"],
      width: 120,
    },
    {
      title: "物料名称",
      dataIndex: ["materiel", "name"],
      width: 150,
    },
    {
      title: "规格型号",
      dataIndex: ["materiel", "specification"],
      width: 120,
    },
    {
      title: "库存数量",
      dataIndex: "quantity",
      width: 100,
    },
    {
      title: "批号",
      dataIndex: "batch_no",
      width: 120,
    },
    {
      title: "单位",
      dataIndex: ["materiel", "unit"],
      width: 80,
    },
    {
      title: "仓库",
      dataIndex: ["warehouse", "name"],
      width: 120,
    },
  ];

  // 表格实例
  const tableRef = ref<InstanceType<typeof BaseTable>>();

  // API接口
  const query = useApiTrpc().admin.stock.queryStock.query;

  // 搜索处理
  const handleSearch = () => {
    // 刷新表格数据
    if (tableRef.value) {
      tableRef.value.query();
    }
  };

  // 重置处理
  const handleReset = () => {
    searchForm.materialCode = undefined;
    searchForm.materialName = undefined;
    searchForm.warehouseId = undefined;
    searchForm.batchNo = undefined;
    handleSearch();
  };
</script>

<style scoped>
  .stock-query {
    padding: 24px;
  }
</style>
