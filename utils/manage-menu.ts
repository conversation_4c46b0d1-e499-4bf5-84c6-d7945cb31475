import type { Slot } from "vue";
export type MenuItem = {
  id?: number;
  title: string;
  name: string;
  icon?: Slot;
  disabled?: boolean;
  type: "item" | "group" | "sub";
  sort?: number;
  items?: MenuItem[];
  visible: boolean;
};

export const menuItems: MenuItem[] = [
  { name: "home", title: "首页", type: "item", sort: 1, visible: true },
  {
    name: "system",
    title: "系统管理",
    type: "group",
    sort: 2,
    visible: false,
    items: [
      {
        name: "system/warehouse",
        title: "仓库管理",
        type: "item",
        visible: false,
      },
      {
        name: "system/user",
        title: "用户管理",
        type: "item",
        visible: false,
      },
      {
        name: "system/customer",
        title: "客户管理",
        type: "item",
        visible: false,
      },
      { name: "system/auth", title: "权限管理", type: "item", visible: false },
      { name: "system/role", title: "角色管理", type: "item", visible: false },
    ],
  },
  {
    name: "purchase",
    title: "采购管理",
    type: "group",
    items: [
      {
        name: "purchase/supplier",
        title: "供应商管理",
        type: "item",
        visible: false,
      },
      {
        name: "purchase/order",
        title: "采购订单",
        type: "item",
        visible: false,
      },
    ],
    visible: false,
  },
  {
    name: "materiel",
    title: "物料管理",
    type: "group",
    items: [
      {
        name: "materiel/type",
        title: "物料分类管理",
        type: "item",
        visible: false,
      },
      {
        name: "materiel/materiel",
        title: "物料管理",
        type: "item",
        visible: false,
      },
    ],
    visible: false,
  },
  {
    name: "audit",
    title: "审核管理",
    type: "group",
    items: [
      {
        name: "audit/purchase",
        title: "采购审核",
        type: "item",
        visible: false,
      },
      { name: "audit/sale", title: "销售审核", type: "item", visible: false },
      { name: "audit/return", title: "退货审核", type: "item", visible: false },
    ],
    visible: false,
  },
  {
    name: "production",
    title: "生产管理",
    type: "group",
    items: [
      {
        name: "production/plan",
        title: "生产计划",
        type: "item",
        visible: false,
      },
      {
        name: "production/task",
        title: "生产工单",
        type: "item",
        visible: false,
      },
      {
        name: "production/reporting",
        title: "生产报工",
        type: "item",
        visible: false,
      },
      {
        name: "production/schedule",
        title: "排班管理",
        type: "item",
        visible: false,
      },
      {
        name: "production/performance",
        title: "员工绩效",
        type: "item",
        visible: false,
      },
      {
        name: "production/statistics",
        title: "产量统计",
        type: "item",
        visible: false,
      },
      {
        name: "production/waste",
        title: "废料处理",
        type: "item",
        visible: false,
      },
      {
        name: "production/disinfection",
        title: "消毒处理",
        type: "item",
        visible: false,
      },
    ],
    visible: false,
  },
  {
    name: "stock",
    title: "库存管理",
    type: "group",
    items: [
      { name: "stock/stock", title: "库存查询", type: "item", visible: false },
      {
        name: "stock/purchase",
        title: "采购入库",
        type: "item",
        visible: false,
      },
      { name: "stock/return", title: "退货入库", type: "item", visible: false },
      {
        name: "stock/productionin",
        title: "生产入库",
        type: "item",
        visible: false,
      },
      {
        name: "stock/productionout",
        title: "生产领料",
        type: "item",
        visible: false,
      },
      {
        name: "stock/sale",
        title: "销售出库",
        type: "item",
        visible: false,
      },
      {
        name: "stock/transfer",
        title: "库存调拨",
        type: "item",
        visible: false,
      },
    ],
    visible: false,
  },
  {
    name: "quality",
    title: "质量管理",
    type: "group",
    items: [
      {
        name: "quality/defect",
        title: "常见缺陷",
        type: "item",
        visible: false,
      },
    ],
    visible: false,
  },
];
export const getItems = (menuName: Array<string>) => {
  const menu = deepClone(menuItems);
  menu.forEach((item) => {
    if (item.type === "item") {
      if (menuName.includes(item.name)) {
        item.visible = true;
      }
    } else {
      item.items?.forEach((i) => {
        if (menuName.includes(i.name)) {
          i.visible = true;
          item.visible = true;
        }
      });
    }
    // if (item.type === "group") {
    //   return item.items?.some((i) => menuName.includes(i.name));
    // }
    // return false;
  });
  return menu;
  // return menu.map((item) => {
  //   switch (item.type) {
  //     case "item":
  //       if (menuName.includes(item.name)) {
  //         return item;
  //       }
  //       break;
  //     default:
  //       break;
  //   }
  // });
  // const filterItem = (item: MenuItem) => {
  //   let newItem: MenuItem | undefined = undefined;
  //   switch (item.type) {
  //     case "item":
  //       if (menuName.includes(item.name)) {
  //         newItem = item;
  //       }
  //       break;
  //     default:
  //       if (menuName.includes(item.name)) {
  //         newItem = item;
  //         newItem.items = item.items?.filter((i) => filterItem(i));
  //       }
  //       break;
  //   }
  //   return newItem;
  // };
  // return menu.filter((item) => {
  //   return filterItem(item);
  // });
};

// export const getItems = () =>
//   menuItems.filter((item: MenuItem) => {
//     const permission = ["home", "user"];
//     switch (item.type) {
//       case "item":
//         if (permission.includes(item.name)) {
//           return item;
//         }
//         break;
//       default:
//         if (permission.includes(item.name)) {

//         }
//     }
//     if (item.name == "home" || item.name == "user") {
//       return item;
//     }
//   });
