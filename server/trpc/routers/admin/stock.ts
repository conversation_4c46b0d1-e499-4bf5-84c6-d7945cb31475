import {
  router,
  mergeRouters,
  singleResultProcedure,
  tableResultProcedure,
} from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import {
  salesOutRecordCreateSchema,
  salesOutRecordQuerySchema,
} from "~/schemas/sales";

export const queryPurchaseOrders = tableResultProcedure
  .meta({ permission: ["stock:purchase:query"], authRequired: true })
  .input(z.object({}))
  .query(async ({ input }) => {
    const purchaseOrders = await prisma.purchaseOrder.findManyWithCount({
      take: input.take,
      skip: input.skip,
      where: {
        status: {
          in: ["approved", "partially_received", "completed"],
        },
      },
    });
    return {
      code: 1,
      message: "success",
      data: purchaseOrders,
    };
  });
// 查询库存列表
export const queryStock = tableResultProcedure
  .meta({ permission: ["stock:query"], authRequired: true })
  .input(
    z.object({
      take: z.number().optional(),
      skip: z.number().optional(),
      materialCode: z.string().optional(),
      materialName: z.string().optional(),
      warehouseId: z.number().optional(),
      batchNo: z.string().optional(),
    })
  )
  .query(async ({ input }) => {
    const where = {
      materiel: {
        code: input.materialCode ? { contains: input.materialCode } : undefined,
        name: input.materialName ? { contains: input.materialName } : undefined,
      },
      warehouse_id: input.warehouseId,
      batch_no: input.batchNo ? { contains: input.batchNo } : undefined,
    };

    const stock = await prisma.stock.findManyWithCount({
      where,
      take: input.take,
      skip: input.skip,
      include: {
        materiel: true,
        warehouse: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    return {
      code: 1,
      message: "success",
      data: stock,
    };
  });

// 查询库存列表（按物料分组）
export const queryStockGrouped = tableResultProcedure
  .meta({ permission: ["stock:query"], authRequired: true })
  .input(
    z.object({
      take: z.number().optional(),
      skip: z.number().optional(),
      materialCode: z.string().optional(),
      materialName: z.string().optional(),
      warehouseId: z.number().optional(),
      batchNo: z.string().optional(),
    })
  )
  .query(async ({ input }) => {
    try {
      // 构建查询条件
      const where: any = {
        warehouse_id: input.warehouseId,
        batch_no: input.batchNo ? { contains: input.batchNo } : undefined,
      };

      // 处理物料搜索条件
      if (input.materialCode || input.materialName) {
        const materielConditions: any = {};

        // 如果物料编码和物料名称是相同的值（来自快速搜索）
        if (input.materialCode === input.materialName && input.materialCode) {
          // 使用OR条件同时搜索编码和名称
          materielConditions.OR = [
            { code: { contains: input.materialCode } },
            { name: { contains: input.materialCode } },
          ];
        } else {
          // 分别处理编码和名称搜索
          if (input.materialCode) {
            materielConditions.code = { contains: input.materialCode };
          }
          if (input.materialName && input.materialName !== input.materialCode) {
            materielConditions.name = { contains: input.materialName };
          }
        }

        where.materiel = materielConditions;
      }

      // 先获取所有符合条件的库存记录
      const allStock = await prisma.stock.findMany({
        where,
        include: {
          materiel: true,
          warehouse: true,
        },
        orderBy: {
          updatedAt: "desc",
        },
      });

      // 按物料分组
      const groupedStock = new Map();

      allStock.forEach((stock) => {
        const key = `${stock.materiel_id}`;
        if (!groupedStock.has(key)) {
          groupedStock.set(key, {
            id: stock.materiel_id,
            materiel: stock.materiel,
            totalQuantity: 0,
            batches: [],
          });
        }

        const group = groupedStock.get(key);
        // 确保数量转换为数字
        const quantity =
          typeof stock.quantity === "string"
            ? parseFloat(stock.quantity)
            : Number(stock.quantity);
        group.totalQuantity += quantity;
        group.batches.push({
          id: stock.id,
          batch_no: stock.batch_no,
          quantity: stock.quantity,
          warehouse: stock.warehouse,
          production_date: stock.production_date,
          expiry_date: stock.expiry_date,
          location: stock.location,
          status: stock.status,
          note: stock.note,
          createdAt: stock.createdAt,
          updatedAt: stock.updatedAt,
        });
      });

      // 转换为数组并分页
      const result = Array.from(groupedStock.values());
      const total = result.length;

      // 手动分页
      const skip = input.skip || 0;
      const take = input.take || 10;
      const paginatedResult = result.slice(skip, skip + take);

      return {
        code: 1,
        message: "success",
        data: {
          result: paginatedResult,
          total: total,
        },
      };
    } catch (error) {
      console.error("查询分组库存失败:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "查询库存失败",
      });
    }
  });

// 入库处理
export const stockIn = singleResultProcedure
  .meta({ permission: ["stock:in"], authRequired: true })
  .input(
    z.object({
      materiel_id: z.number(),
      warehouse_id: z.number(),
      batch_no: z.string(),
      quantity: z.number(),
      production_date: z.date().optional(),
      expiry_date: z.date().optional(),
      location: z.string().optional(),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input }) => {
    const { materiel_id, warehouse_id, batch_no, quantity, ...rest } = input;

    // 检查物料是否存在
    const materiel = await prisma.materiel.findUnique({
      where: { id: materiel_id },
    });
    if (!materiel) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "物料不存在",
      });
    }

    // 检查仓库是否存在
    const warehouse = await prisma.warehouse.findUnique({
      where: { id: warehouse_id },
    });
    if (!warehouse) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "仓库不存在",
      });
    }

    // 事务处理：更新或创建库存记录，同时更新物料总库存
    const result = await prisma.$transaction(async (tx) => {
      // 查找现有库存记录
      const existingStock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id,
            batch_no,
          },
        },
      });

      if (existingStock) {
        // 更新现有库存记录
        const updatedStock = await tx.stock.update({
          where: { id: existingStock.id },
          data: {
            quantity: { increment: quantity },
            ...rest,
          },
        });

        // 更新物料总库存
        await tx.materiel.update({
          where: { id: materiel_id },
          data: {
            stock: { increment: quantity },
          },
        });

        return updatedStock;
      } else {
        // 创建新的库存记录
        const newStock = await tx.stock.create({
          data: {
            materiel_id,
            warehouse_id,
            batch_no,
            quantity,
            ...rest,
          },
        });

        // 更新物料总库存
        await tx.materiel.update({
          where: { id: materiel_id },
          data: {
            stock: { increment: quantity },
          },
        });

        return newStock;
      }
    });

    return {
      code: 1,
      message: "入库成功",
      data: result,
    };
  });

// 出库处理
export const stockOut = singleResultProcedure
  .meta({ permission: ["stock:out"], authRequired: true })
  .input(
    z.object({
      materiel_id: z.number(),
      warehouse_id: z.number(),
      batch_no: z.string(),
      quantity: z.number(),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input }) => {
    const { materiel_id, warehouse_id, batch_no, quantity, note } = input;

    // 事务处理：更新库存记录，同时更新物料总库存
    const result = await prisma.$transaction(async (tx) => {
      // 查找库存记录
      const stock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id,
            batch_no,
          },
        },
      });

      if (!stock) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存记录不存在",
        });
      }

      if (stock.quantity.lessThan(quantity)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存不足",
        });
      }

      // 更新库存记录
      const updatedStock = await tx.stock.update({
        where: { id: stock.id },
        data: {
          quantity: { decrement: quantity },
          note: note,
        },
      });

      // 更新物料总库存
      await tx.materiel.update({
        where: { id: materiel_id },
        data: {
          stock: { decrement: quantity },
        },
      });

      return updatedStock;
    });

    return {
      code: 1,
      message: "出库成功",
      data: result,
    };
  });

// 库存调拨处理
export const stockTransfer = singleResultProcedure
  .meta({ permission: ["stock:transfer"], authRequired: true })
  .input(
    z.object({
      materiel_id: z.number(),
      source_warehouse_id: z.number(),
      target_warehouse_id: z.number(),
      source_batch_no: z.string(),
      target_batch_no: z.string(),
      quantity: z.number(),
      note: z.string().optional(),
    })
  )
  .mutation(async ({ input }) => {
    const {
      materiel_id,
      source_warehouse_id,
      target_warehouse_id,
      source_batch_no,
      target_batch_no,
      quantity,
      note,
    } = input;

    // 检查源仓库和目标仓库不能相同
    if (source_warehouse_id === target_warehouse_id) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "源仓库和目标仓库不能相同",
      });
    }

    // 事务处理：从源仓库出库，同时向目标仓库入库
    const result = await prisma.$transaction(async (tx) => {
      // 查找源库存记录
      const sourceStock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id: source_warehouse_id,
            batch_no: source_batch_no,
          },
        },
      });

      if (!sourceStock) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "源库存记录不存在",
        });
      }

      if (sourceStock.quantity.lessThan(quantity)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "源库存不足",
        });
      }

      // 从源仓库减少库存
      await tx.stock.update({
        where: { id: sourceStock.id },
        data: {
          quantity: { decrement: quantity },
          note: `调拨至${target_warehouse_id}仓库: ${note || ""}`,
        },
      });

      // 查找目标库存记录
      const targetStock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id: target_warehouse_id,
            batch_no: target_batch_no,
          },
        },
      });

      // 向目标仓库增加库存
      if (targetStock) {
        // 更新现有库存记录
        await tx.stock.update({
          where: { id: targetStock.id },
          data: {
            quantity: { increment: quantity },
            note: `从${source_warehouse_id}仓库调拨: ${note || ""}`,
          },
        });
      } else {
        // 创建新的库存记录
        await tx.stock.create({
          data: {
            materiel_id,
            warehouse_id: target_warehouse_id,
            batch_no: target_batch_no,
            quantity,
            note: `从${source_warehouse_id}仓库调拨: ${note || ""}`,
          },
        });
      }

      // 总库存不变，因为只是在仓库间调拨
      return { success: true };
    });

    return {
      code: 1,
      message: "库存调拨成功",
      data: result,
    };
  });

// 销售出库处理
export const salesOut = singleResultProcedure
  .meta({ permission: ["stock:sales:out"], authRequired: true })
  .input(salesOutRecordCreateSchema)
  .mutation(async ({ input, ctx }) => {
    const {
      customer_id,
      materiel_id,
      warehouse_id,
      batch_no,
      quantity,
      order_no,
      note,
    } = input;

    // 检查客户是否存在
    const customer = await prisma.customer.findUnique({
      where: { id: customer_id },
    });
    if (!customer) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "客户不存在",
      });
    }

    // 检查物料是否存在
    const materiel = await prisma.materiel.findUnique({
      where: { id: materiel_id },
    });
    if (!materiel) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "物料不存在",
      });
    }

    // 检查仓库是否存在
    const warehouse = await prisma.warehouse.findUnique({
      where: { id: warehouse_id },
    });
    if (!warehouse) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "仓库不存在",
      });
    }

    // 事务处理：更新库存记录，同时更新物料总库存，创建销售出库记录
    const result = await prisma.$transaction(async (tx) => {
      // 查找库存记录
      const stock = await tx.stock.findUnique({
        where: {
          materiel_id_warehouse_id_batch_no: {
            materiel_id,
            warehouse_id,
            batch_no,
          },
        },
      });

      if (!stock) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存记录不存在",
        });
      }

      if (stock.quantity.lessThan(quantity)) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "库存不足",
        });
      }

      // 更新库存记录
      const updatedStock = await tx.stock.update({
        where: { id: stock.id },
        data: {
          quantity: { decrement: quantity },
          note: `销售出库: ${order_no}, ${note || ""}`,
        },
      });

      // 更新物料总库存
      await tx.materiel.update({
        where: { id: materiel_id },
        data: {
          stock: { decrement: quantity },
        },
      });

      // 创建销售出库记录
      const salesOutRecord = await tx.salesOutRecord.create({
        data: {
          customer_id,
          materiel_id,
          warehouse_id,
          batch_no,
          quantity,
          order_no,
          note,
          user_id: ctx.user.id,
        },
      });

      return {
        updatedStock,
        salesOutRecord,
      };
    });

    return {
      code: 1,
      message: "销售出库成功",
      data: result,
    };
  });

// 查询销售出库记录
export const querySalesOutRecords = tableResultProcedure
  .meta({ permission: ["stock:sales:query"], authRequired: true })
  .input(salesOutRecordQuerySchema)
  .query(async ({ input }) => {
    const where: any = {};

    if (input.customer_id) {
      where.customer_id = input.customer_id;
    }

    if (input.materiel_id) {
      where.materiel_id = input.materiel_id;
    }

    if (input.warehouse_id) {
      where.warehouse_id = input.warehouse_id;
    }

    if (input.order_no) {
      where.order_no = {
        contains: input.order_no,
      };
    }

    if (input.startDate && input.endDate) {
      where.createdAt = {
        gte: new Date(input.startDate),
        lte: new Date(input.endDate),
      };
    }

    const salesOutRecords = await prisma.salesOutRecord.findManyWithCount({
      where,
      take: input.take,
      skip: input.skip,
      include: {
        customer: true,
        materiel: true,
        warehouse: true,
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return {
      code: 1,
      message: "success",
      data: salesOutRecords,
    };
  });

export default mergeRouters(
  router({
    queryStock,
    queryStockGrouped,
    stockIn,
    stockOut,
    stockTransfer,
    salesOut,
    querySalesOutRecords,
  })
);
